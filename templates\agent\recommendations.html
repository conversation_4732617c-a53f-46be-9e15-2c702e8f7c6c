{% extends "base.html" %}

{% block title %}智能推荐{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <h1>智能推荐</h1>
                <p class="text-muted">基于您的使用习惯和部门需求的个性化物资推荐</p>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-lightbulb"></i> 为您推荐
                        <button class="btn btn-sm btn-outline-secondary float-right" onclick="location.reload()">
                            <i class="fas fa-sync"></i> 刷新推荐
                        </button>
                    </h5>
                </div>
                <div class="card-body">
                    {% if recommendations %}
                        <div class="row">
                            {% for rec in recommendations %}
                            <div class="col-md-6 col-lg-4 mb-4">
                                <div class="card h-100 border-left-primary">
                                    <div class="card-body">
                                        <h6 class="card-title">
                                            {{ rec.material_name }}
                                            <span class="badge badge-info ml-2">推荐度: {{ "%.1f"|format(rec.score * 100) }}%</span>
                                        </h6>
                                        <p class="card-text">
                                            <strong>类别:</strong> {{ rec.category }}<br>
                                            <strong>库存:</strong> {{ rec.remaining_quantity }} 件<br>
                                            <strong>单价:</strong> ¥{{ "%.2f"|format(rec.unit_price) }}
                                        </p>
                                        {% if rec.reason %}
                                            <p class="text-muted small">
                                                <i class="fas fa-info-circle"></i> {{ rec.reason }}
                                            </p>
                                        {% endif %}
                                        <div class="mt-3">
                                            <button class="btn btn-primary btn-sm" onclick="requestMaterial({{ rec.material_id }}, '{{ rec.material_name }}')">
                                                <i class="fas fa-plus"></i> 申请物资
                                            </button>
                                            <button class="btn btn-outline-info btn-sm ml-2" onclick="viewMaterialDetail({{ rec.material_id }})">
                                                <i class="fas fa-eye"></i> 查看详情
                                            </button>
                                        </div>
                                    </div>
                                    <div class="card-footer text-muted small">
                                        推荐时间: {{ rec.created_at.strftime('%Y-%m-%d %H:%M') if rec.created_at else '' }}
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-lightbulb fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">暂无推荐</h5>
                            <p class="text-muted">系统正在学习您的使用习惯，稍后会为您提供个性化推荐</p>
                            <button class="btn btn-outline-primary" onclick="generateRecommendations()">
                                <i class="fas fa-magic"></i> 生成推荐
                            </button>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- 推荐说明 -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-question-circle"></i> 推荐说明
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <h6><i class="fas fa-users text-primary"></i> 部门热门</h6>
                            <p class="small text-muted">基于您所在部门的热门物资使用情况</p>
                        </div>
                        <div class="col-md-4">
                            <h6><i class="fas fa-history text-success"></i> 历史偏好</h6>
                            <p class="small text-muted">根据您的历史申请记录分析偏好</p>
                        </div>
                        <div class="col-md-4">
                            <h6><i class="fas fa-chart-line text-info"></i> 趋势分析</h6>
                            <p class="small text-muted">结合全局使用趋势和季节性需求</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 申请物资模态框 -->
<div class="modal fade" id="requestModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">申请物资</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form id="requestForm">
                <div class="modal-body">
                    <input type="hidden" id="material_id" name="material_id">
                    <div class="form-group">
                        <label>物资名称</label>
                        <input type="text" class="form-control" id="material_name" readonly>
                    </div>
                    <div class="form-group">
                        <label for="quantity">申请数量</label>
                        <input type="number" class="form-control" id="quantity" name="quantity" min="1" required>
                    </div>
                    <div class="form-group">
                        <label for="reason">申请理由</label>
                        <textarea class="form-control" id="reason" name="reason" rows="3" placeholder="请简要说明申请理由"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">提交申请</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function requestMaterial(materialId, materialName) {
    $('#material_id').val(materialId);
    $('#material_name').val(materialName);
    $('#quantity').val(1);
    $('#reason').val('');
    $('#requestModal').modal('show');
}

function viewMaterialDetail(materialId) {
    // 跳转到物资详情页面
    window.location.href = `/material/detail/${materialId}`;
}

function generateRecommendations() {
    // 手动触发推荐生成
    $.post('/agent/api/run/SmartRecommendation', function(data) {
        if (data.error) {
            alert('生成推荐失败: ' + data.error);
        } else {
            alert('推荐生成中，请稍后刷新页面查看结果');
            setTimeout(function() {
                location.reload();
            }, 3000);
        }
    });
}

// 处理申请表单提交
$('#requestForm').on('submit', function(e) {
    e.preventDefault();
    
    const formData = {
        material_id: $('#material_id').val(),
        quantity: $('#quantity').val(),
        reason: $('#reason').val()
    };
    
    $.post('/material/request', formData, function(data) {
        if (data.success) {
            alert('申请提交成功！');
            $('#requestModal').modal('hide');
        } else {
            alert('申请失败: ' + (data.error || '未知错误'));
        }
    });
});
</script>

<style>
.border-left-primary {
    border-left: 4px solid #007bff !important;
}

.card-title {
    color: #495057;
    font-weight: 600;
}

.badge-info {
    background-color: #17a2b8;
}
</style>
{% endblock %}
