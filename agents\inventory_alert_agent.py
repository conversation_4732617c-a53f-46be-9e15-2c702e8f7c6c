from datetime import datetime, timedelta
from typing import List, Dict, Any
from .base_agent import BaseAgent
from dao.material_dao import MaterialDAO
from services.notification_service import NotificationService


class InventoryAlertAgent(BaseAgent):
    """库存预警Agent"""

    def __init__(self):
        super().__init__("InventoryAlert")
        self.material_dao = MaterialDAO()
        self.notification_service = NotificationService()
        self.low_stock_threshold = 10  # 低库存阈值

    def execute(self) -> Dict[str, Any]:
        """执行库存检查"""
        results = {
            'low_stock_items': [],
            'notifications_sent': 0,
            'status': 'success'
        }

        try:
            # 检查低库存物资
            low_stock_items = self.check_low_stock()
            results['low_stock_items'] = low_stock_items

            # 发送通知
            if low_stock_items:
                notifications_sent = self.send_notifications(low_stock_items)
                results['notifications_sent'] = notifications_sent

            self.log_action("库存检查完成", f"发现{len(low_stock_items)}项低库存物资")

        except Exception as e:
            self.logger.error(f"库存检查失败: {str(e)}")
            results['error'] = str(e)
            results['status'] = 'failed'

        return results

    def check_low_stock(self) -> List[Dict]:
        """检查低库存物资"""
        try:
            sql = """
            SELECT id, name, category, remaining_quantity, quantity, status
            FROM materials 
            WHERE remaining_quantity <= %s AND status = 'available'
            ORDER BY remaining_quantity ASC
            """
            return self.material_dao.db.execute_query(sql, (self.low_stock_threshold,))
        except Exception as e:
            self.logger.error(f"检查低库存失败: {e}")
            return []

    def send_notifications(self, low_stock_items: List[Dict]) -> int:
        """发送低库存通知"""
        try:
            if not low_stock_items:
                return 0

            # 构建通知消息
            message_parts = []
            for item in low_stock_items[:10]:  # 只显示前10项
                message_parts.append(
                    f"• {item['name']} (剩余: {item['remaining_quantity']}/{item['quantity']})"
                )
            
            if len(low_stock_items) > 10:
                message_parts.append(f"... 还有{len(low_stock_items) - 10}项物资库存不足")

            message = f"发现{len(low_stock_items)}项物资库存不足，请及时补充：\n\n" + "\n".join(message_parts)

            # 发送通知
            self.notification_service.send_admin_notification(
                title="库存预警",
                message=message,
                data=low_stock_items
            )

            return 1

        except Exception as e:
            self.logger.error(f"发送通知失败: {e}")
            return 0

    def set_threshold(self, threshold: int):
        """设置低库存阈值"""
        self.low_stock_threshold = threshold
        self.log_action("更新配置", f"低库存阈值设置为: {threshold}")
