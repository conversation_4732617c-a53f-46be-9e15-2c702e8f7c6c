{% extends "base.html" %}

{% block title %}系统通知{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <h1>系统通知</h1>
                <p class="text-muted">查看系统发送的各类通知消息</p>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bell"></i> 通知列表
                        <span class="badge badge-primary ml-2">{{ unread_count }} 未读</span>
                        <button class="btn btn-sm btn-outline-secondary float-right" onclick="location.reload()">
                            <i class="fas fa-sync"></i> 刷新
                        </button>
                    </h5>
                </div>
                <div class="card-body">
                    {% if notifications %}
                        <div class="list-group">
                            {% for notification in notifications %}
                            <div class="list-group-item {% if not notification.is_read %}list-group-item-light border-left-primary{% endif %}">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">
                                        {% if not notification.is_read %}
                                            <i class="fas fa-circle text-primary" style="font-size: 8px;"></i>
                                        {% endif %}
                                        {{ notification.title }}
                                        <span class="badge badge-{{ 'danger' if notification.type == 'admin' else 'info' }} ml-2">
                                            {{ '管理员' if notification.type == 'admin' else '系统' }}
                                        </span>
                                    </h6>
                                    <small class="text-muted">{{ notification.created_at.strftime('%Y-%m-%d %H:%M') if notification.created_at else '' }}</small>
                                </div>
                                <p class="mb-2">{{ notification.message }}</p>
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">
                                        {% if notification.data %}
                                            <button class="btn btn-sm btn-outline-info" onclick="showNotificationData({{ notification.id }}, '{{ notification.data|e }}')">
                                                <i class="fas fa-info-circle"></i> 查看详情
                                            </button>
                                        {% endif %}
                                    </small>
                                    {% if not notification.is_read %}
                                        <button class="btn btn-sm btn-outline-success" onclick="markAsRead({{ notification.id }})">
                                            <i class="fas fa-check"></i> 标记已读
                                        </button>
                                    {% endif %}
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-bell-slash fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">暂无通知</h5>
                            <p class="text-muted">系统会在这里显示重要通知和提醒</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 通知详情模态框 -->
<div class="modal fade" id="notificationModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">通知详情</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="notification-detail-content"></div>
            </div>
        </div>
    </div>
</div>

<script>
function markAsRead(notificationId) {
    $.post(`/agent/api/notifications/mark_read/${notificationId}`, function(data) {
        if (data.success) {
            location.reload();
        } else {
            alert('操作失败: ' + (data.error || '未知错误'));
        }
    });
}

function showNotificationData(notificationId, data) {
    try {
        const parsedData = JSON.parse(data);
        let content = '';
        
        if (Array.isArray(parsedData)) {
            // 如果是数组，显示为表格
            if (parsedData.length > 0) {
                content = '<div class="table-responsive"><table class="table table-sm">';
                
                // 表头
                const headers = Object.keys(parsedData[0]);
                content += '<thead><tr>';
                headers.forEach(header => {
                    content += `<th>${header}</th>`;
                });
                content += '</tr></thead>';
                
                // 表体
                content += '<tbody>';
                parsedData.forEach(item => {
                    content += '<tr>';
                    headers.forEach(header => {
                        content += `<td>${item[header] || '-'}</td>`;
                    });
                    content += '</tr>';
                });
                content += '</tbody></table></div>';
            }
        } else {
            // 如果是对象，显示为键值对
            content = '<dl class="row">';
            Object.entries(parsedData).forEach(([key, value]) => {
                content += `<dt class="col-sm-3">${key}:</dt>`;
                content += `<dd class="col-sm-9">${JSON.stringify(value)}</dd>`;
            });
            content += '</dl>';
        }
        
        $('#notification-detail-content').html(content);
    } catch (e) {
        $('#notification-detail-content').html(`<pre>${data}</pre>`);
    }
    
    $('#notificationModal').modal('show');
}

// 定期检查未读通知数量
setInterval(function() {
    $.get('/agent/api/notifications/unread_count', function(data) {
        if (data.count !== undefined) {
            const currentCount = parseInt($('.badge-primary').text());
            if (data.count !== currentCount) {
                location.reload(); // 有新通知时刷新页面
            }
        }
    });
}, 30000); // 每30秒检查一次
</script>

<style>
.border-left-primary {
    border-left: 4px solid #007bff !important;
}

.list-group-item-light {
    background-color: #f8f9fa;
}
</style>
{% endblock %}
