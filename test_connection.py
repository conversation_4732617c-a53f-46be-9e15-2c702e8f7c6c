#!/usr/bin/env python3
# -*- coding: utf-8 -*-

try:
    import pymysql
    print("✅ PyMySQL 导入成功")
    
    # 测试数据库连接
    conn = pymysql.connect(
        host='localhost',
        user='root',
        password='lax217652',
        database='goods'
    )
    print("✅ 数据库连接成功！")
    
    cursor = conn.cursor()
    cursor.execute("SELECT VERSION()")
    version = cursor.fetchone()
    print(f"✅ MySQL版本: {version[0]}")
    
    conn.close()
    print("✅ 连接测试完成")
    
except ImportError as e:
    print(f"❌ PyMySQL 导入失败: {e}")
except Exception as e:
    print(f"❌ 数据库连接失败: {e}")
