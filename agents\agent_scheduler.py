from utils.simple_scheduler import schedule
import time
import threading
import json
from datetime import datetime
from typing import List, Dict, Any
from .base_agent import BaseAgent
from dao.database import db


class AgentScheduler:
    """Agent调度器"""

    def __init__(self):
        self.agents = {}
        self.running = False
        self.scheduler_thread = None
        self.db = db

    def register_agent(self, agent: BaseAgent, schedule_time: str, interval: str = 'daily'):
        """注册Agent"""
        self.agents[agent.name] = {
            'agent': agent,
            'schedule_time': schedule_time,
            'interval': interval,
            'last_run': None,
            'status': 'registered'
        }

        # 设置调度
        try:
            if interval == 'daily':
                schedule.every().day().at(schedule_time).do(self._run_agent, agent.name)
            elif interval == 'hourly':
                # 对于小时调度，schedule_time应该是分钟数，如 "30" 表示每小时的30分
                schedule.every().hour().at(f":{schedule_time}").do(self._run_agent, agent.name)
            elif interval == 'weekly':
                # 对于周调度，可以指定星期几和时间，如 "monday.08:00"
                if '.' in schedule_time:
                    schedule.every().week().at(schedule_time).do(self._run_agent, agent.name)
                else:
                    schedule.every().week().at(schedule_time).do(self._run_agent, agent.name)
            
            print(f"Agent {agent.name} 已注册，调度时间: {interval} at {schedule_time}")
            
        except Exception as e:
            print(f"注册Agent {agent.name} 失败: {e}")

    def _run_agent(self, agent_name: str):
        """运行Agent"""
        if agent_name not in self.agents:
            return

        agent_info = self.agents[agent_name]
        agent = agent_info['agent']
        
        print(f"开始执行Agent: {agent_name}")
        start_time = datetime.now()
        
        try:
            # 更新状态
            self.agents[agent_name]['status'] = 'running'
            self.agents[agent_name]['last_run'] = start_time
            
            # 执行Agent
            result = agent.execute()
            
            # 记录执行结果
            execution_time = (datetime.now() - start_time).total_seconds()
            self._log_agent_execution(agent_name, 'success', result, execution_time)
            
            # 更新状态
            self.agents[agent_name]['status'] = 'completed'
            
            print(f"Agent {agent_name} 执行成功，耗时: {execution_time:.2f}秒")
            
        except Exception as e:
            # 记录错误
            execution_time = (datetime.now() - start_time).total_seconds()
            self._log_agent_execution(agent_name, 'failed', {'error': str(e)}, execution_time)
            
            # 更新状态
            self.agents[agent_name]['status'] = 'failed'
            
            print(f"Agent {agent_name} 执行失败: {e}")

    def start_scheduler(self):
        """启动调度器"""
        if self.running:
            print("调度器已在运行中")
            return
            
        self.running = True
        self.scheduler_thread = threading.Thread(target=self._run_scheduler, daemon=True)
        self.scheduler_thread.start()
        print("Agent调度器已启动")

    def stop_scheduler(self):
        """停止调度器"""
        self.running = False
        if self.scheduler_thread and self.scheduler_thread.is_alive():
            self.scheduler_thread.join(timeout=5)
        print("Agent调度器已停止")

    def _run_scheduler(self):
        """运行调度循环"""
        while self.running:
            try:
                schedule.run_pending()
                time.sleep(60)  # 每分钟检查一次
            except Exception as e:
                print(f"调度器运行错误: {e}")
                time.sleep(60)

    def _log_agent_execution(self, agent_name: str, status: str, result: Dict[str, Any], execution_time: float):
        """记录Agent执行结果"""
        try:
            # 确保日志表存在
            self._ensure_execution_logs_table()
            
            sql = """
            INSERT INTO agent_execution_logs 
            (agent_name, status, result, execution_time, executed_at)
            VALUES (%s, %s, %s, %s, %s)
            """
            self.db.execute_insert(sql, (
                agent_name,
                status,
                json.dumps(result, ensure_ascii=False, default=str),
                execution_time,
                datetime.now()
            ))
            
        except Exception as e:
            print(f"记录Agent执行日志失败: {e}")

    def _ensure_execution_logs_table(self):
        """确保执行日志表存在"""
        try:
            create_table_sql = """
            CREATE TABLE IF NOT EXISTS agent_execution_logs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                agent_name VARCHAR(100) NOT NULL,
                status ENUM('success', 'failed') NOT NULL,
                result JSON,
                execution_time DECIMAL(10,3),
                executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_agent_name (agent_name),
                INDEX idx_executed_at (executed_at)
            )
            """
            self.db.execute_update(create_table_sql)
        except Exception as e:
            print(f"创建执行日志表失败: {e}")

    def manual_run_agent(self, agent_name: str) -> Dict[str, Any]:
        """手动执行Agent"""
        if agent_name not in self.agents:
            return {'error': f'Agent {agent_name} 不存在'}
        
        try:
            # 在新线程中执行，避免阻塞
            thread = threading.Thread(target=self._run_agent, args=(agent_name,))
            thread.start()
            
            return {'message': f'Agent {agent_name} 已开始执行'}
            
        except Exception as e:
            return {'error': f'执行Agent失败: {str(e)}'}

    def get_agent_status(self) -> Dict[str, Any]:
        """获取所有Agent状态"""
        status_info = {}
        
        for name, info in self.agents.items():
            status_info[name] = {
                'status': info['status'],
                'schedule_time': info['schedule_time'],
                'interval': info['interval'],
                'last_run': info['last_run'].isoformat() if info['last_run'] else None
            }
        
        return {
            'scheduler_running': self.running,
            'agents': status_info,
            'total_agents': len(self.agents)
        }

    def get_execution_history(self, agent_name: str = None, limit: int = 50) -> List[Dict]:
        """获取执行历史"""
        try:
            if agent_name:
                sql = """
                SELECT * FROM agent_execution_logs 
                WHERE agent_name = %s 
                ORDER BY executed_at DESC 
                LIMIT %s
                """
                return self.db.execute_query(sql, (agent_name, limit))
            else:
                sql = """
                SELECT * FROM agent_execution_logs 
                ORDER BY executed_at DESC 
                LIMIT %s
                """
                return self.db.execute_query(sql, (limit,))
                
        except Exception as e:
            print(f"获取执行历史失败: {e}")
            return []

    def get_agent_statistics(self) -> Dict[str, Any]:
        """获取Agent统计信息"""
        try:
            # 总执行次数
            total_sql = "SELECT COUNT(*) as total FROM agent_execution_logs"
            total_result = self.db.execute_query_one(total_sql)
            total_executions = total_result['total'] if total_result else 0
            
            # 成功率
            success_sql = "SELECT COUNT(*) as success FROM agent_execution_logs WHERE status = 'success'"
            success_result = self.db.execute_query_one(success_sql)
            success_count = success_result['success'] if success_result else 0
            
            success_rate = (success_count / total_executions * 100) if total_executions > 0 else 0
            
            # 各Agent执行统计
            agent_stats_sql = """
            SELECT agent_name, 
                   COUNT(*) as total_executions,
                   SUM(CASE WHEN status = 'success' THEN 1 ELSE 0 END) as success_count,
                   AVG(execution_time) as avg_execution_time,
                   MAX(executed_at) as last_execution
            FROM agent_execution_logs 
            GROUP BY agent_name
            """
            agent_stats = self.db.execute_query(agent_stats_sql)
            
            return {
                'total_executions': total_executions,
                'success_count': success_count,
                'success_rate': round(success_rate, 2),
                'agent_statistics': agent_stats
            }
            
        except Exception as e:
            print(f"获取Agent统计信息失败: {e}")
            return {}
