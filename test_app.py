#!/usr/bin/env python3
# -*- coding: utf-8 -*-

print("开始测试应用导入...")

try:
    from flask import Flask
    print("✅ Flask 导入成功")
except ImportError as e:
    print(f"❌ Flask 导入失败: {e}")

try:
    from config import Config
    print("✅ Config 导入成功")
except ImportError as e:
    print(f"❌ Config 导入失败: {e}")

try:
    from controllers.auth_controller import auth_bp
    print("✅ auth_controller 导入成功")
except ImportError as e:
    print(f"❌ auth_controller 导入失败: {e}")

try:
    from controllers.material_controller import material_bp
    print("✅ material_controller 导入成功")
except ImportError as e:
    print(f"❌ material_controller 导入失败: {e}")

try:
    from controllers.report_controller import report_bp
    print("✅ report_controller 导入成功")
except ImportError as e:
    print(f"❌ report_controller 导入失败: {e}")

try:
    from controllers.agent_controller import agent_bp
    print("✅ agent_controller 导入成功")
except ImportError as e:
    print(f"❌ agent_controller 导入失败: {e}")

try:
    from agents.agent_scheduler import AgentScheduler
    print("✅ AgentScheduler 导入成功")
except ImportError as e:
    print(f"❌ AgentScheduler 导入失败: {e}")

print("导入测试完成")
