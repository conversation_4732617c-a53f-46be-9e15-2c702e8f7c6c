#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from flask import Flask, redirect, url_for, session
from config import Config
import logging
import os

print("创建Flask应用...")

app = Flask(__name__)
app.config.from_object(Config)

print("配置日志...")

# 配置日志
if not os.path.exists('logs'):
    os.makedirs('logs')

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s %(levelname)s %(name)s: %(message)s',
    handlers=[
        logging.FileHandler('logs/agent.log'),
        logging.StreamHandler()
    ]
)

print("注册蓝图...")

# 注册蓝图
from controllers.auth_controller import auth_bp
from controllers.material_controller import material_bp
from controllers.report_controller import report_bp
from controllers.agent_controller import agent_bp

app.register_blueprint(auth_bp, url_prefix='/auth')
app.register_blueprint(material_bp)
app.register_blueprint(report_bp)
app.register_blueprint(agent_bp)

print("设置路由...")

# 根路由
@app.route('/')
def index():
    if 'user_id' in session:
        return redirect(url_for('material.dashboard'))
    return redirect(url_for('auth.login'))

print("启动应用...")

if __name__ == '__main__':
    print("应用即将启动在 http://localhost:5000")
    app.run(debug=True, host='0.0.0.0', port=5000)
