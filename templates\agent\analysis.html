{% extends "base.html" %}

{% block title %}数据分析结果{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-chart-bar"></i> 数据分析结果</h2>
                <div>
                    <button class="btn btn-primary" onclick="refreshAnalysis()">
                        <i class="fas fa-sync"></i> 刷新数据
                    </button>
                    <a href="/agent/dashboard" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> 返回监控面板
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- 分析概览 -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-center border-primary">
                <div class="card-body">
                    <h3 class="text-primary" id="total-materials">{{ analysis_data.total_materials or 0 }}</h3>
                    <p class="text-muted">物资总数</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center border-success">
                <div class="card-body">
                    <h3 class="text-success" id="total-requests">{{ analysis_data.total_requests or 0 }}</h3>
                    <p class="text-muted">申请总数</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center border-warning">
                <div class="card-body">
                    <h3 class="text-warning" id="low-stock-count">{{ analysis_data.low_stock_count or 0 }}</h3>
                    <p class="text-muted">低库存物资</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center border-info">
                <div class="card-body">
                    <h3 class="text-info" id="total-value">¥{{ analysis_data.total_value or 0 }}</h3>
                    <p class="text-muted">物资总价值</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 分析图表 -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-pie-chart"></i> 物资类别分布</h5>
                </div>
                <div class="card-body">
                    <canvas id="categoryChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-bar-chart"></i> 月度申请趋势</h5>
                </div>
                <div class="card-body">
                    <canvas id="trendChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- 热门物资排行 -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-fire"></i> 热门物资排行</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>排名</th>
                                    <th>物资名称</th>
                                    <th>申请次数</th>
                                    <th>申请总量</th>
                                </tr>
                            </thead>
                            <tbody id="popular-materials">
                                {% if analysis_data.popular_materials %}
                                    {% for material in analysis_data.popular_materials %}
                                    <tr>
                                        <td>{{ loop.index }}</td>
                                        <td>{{ material.name }}</td>
                                        <td>{{ material.request_count }}</td>
                                        <td>{{ material.total_quantity }}</td>
                                    </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="4" class="text-center text-muted">暂无数据</td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-exclamation-triangle"></i> 库存预警</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>物资名称</th>
                                    <th>当前库存</th>
                                    <th>预警阈值</th>
                                    <th>状态</th>
                                </tr>
                            </thead>
                            <tbody id="low-stock-materials">
                                {% if analysis_data.low_stock_materials %}
                                    {% for material in analysis_data.low_stock_materials %}
                                    <tr>
                                        <td>{{ material.name }}</td>
                                        <td>{{ material.remaining_quantity }}</td>
                                        <td>{{ material.threshold or 10 }}</td>
                                        <td>
                                            {% if material.remaining_quantity <= 5 %}
                                                <span class="badge badge-danger">严重不足</span>
                                            {% elif material.remaining_quantity <= 10 %}
                                                <span class="badge badge-warning">库存不足</span>
                                            {% else %}
                                                <span class="badge badge-success">正常</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="4" class="text-center text-muted">暂无预警</td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 用户活跃度分析 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-users"></i> 用户活跃度分析</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>用户名</th>
                                    <th>申请次数</th>
                                    <th>申请总量</th>
                                    <th>最近申请时间</th>
                                    <th>活跃度</th>
                                </tr>
                            </thead>
                            <tbody id="user-activity">
                                {% if analysis_data.user_activity %}
                                    {% for user in analysis_data.user_activity %}
                                    <tr>
                                        <td>{{ user.username }}</td>
                                        <td>{{ user.request_count }}</td>
                                        <td>{{ user.total_quantity }}</td>
                                        <td>{{ user.last_request_time or '无' }}</td>
                                        <td>
                                            {% if user.request_count >= 10 %}
                                                <span class="badge badge-success">高</span>
                                            {% elif user.request_count >= 5 %}
                                                <span class="badge badge-warning">中</span>
                                            {% else %}
                                                <span class="badge badge-secondary">低</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="5" class="text-center text-muted">暂无数据</td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// 物资类别分布图
function initCategoryChart() {
    const ctx = document.getElementById('categoryChart').getContext('2d');
    const categoryData = {{ analysis_data.category_distribution | tojson | safe }};
    
    if (categoryData && categoryData.length > 0) {
        new Chart(ctx, {
            type: 'pie',
            data: {
                labels: categoryData.map(item => item.category),
                datasets: [{
                    data: categoryData.map(item => item.count),
                    backgroundColor: [
                        '#FF6384',
                        '#36A2EB',
                        '#FFCE56',
                        '#4BC0C0',
                        '#9966FF'
                    ]
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }
}

// 月度申请趋势图
function initTrendChart() {
    const ctx = document.getElementById('trendChart').getContext('2d');
    const trendData = {{ analysis_data.monthly_trend | tojson | safe }};
    
    if (trendData && trendData.length > 0) {
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: trendData.map(item => item.month),
                datasets: [{
                    label: '申请数量',
                    data: trendData.map(item => item.count),
                    borderColor: '#36A2EB',
                    backgroundColor: 'rgba(54, 162, 235, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }
}

// 刷新分析数据
function refreshAnalysis() {
    location.reload();
}

// 页面加载完成后初始化图表
$(document).ready(function() {
    initCategoryChart();
    initTrendChart();
});
</script>
{% endblock %}
