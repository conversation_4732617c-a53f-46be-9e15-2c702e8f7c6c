from flask import Blueprint, render_template, request, jsonify, session, redirect, url_for, current_app
from functools import wraps
from services.notification_service import NotificationService

agent_bp = Blueprint('agent', __name__, url_prefix='/agent')


def get_agent_service():
    """获取Agent服务实例"""
    return current_app.config.get('AGENT_SERVICE')

def admin_required(f):
    """管理员权限装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            return redirect(url_for('auth.login'))
        if session.get('role') != 'admin':
            return jsonify({'error': '需要管理员权限'}), 403
        return f(*args, **kwargs)
    return decorated_function

def login_required(f):
    """登录验证装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            return redirect(url_for('auth.login'))
        return f(*args, **kwargs)
    return decorated_function

@agent_bp.route('/dashboard')
@admin_required
def dashboard():
    """Agent监控面板"""
    try:
        agent_service = get_agent_service()
        if not agent_service:
            return render_template('error.html', error='Agent服务未初始化')

        # 获取Agent状态
        agent_status = agent_service.get_agent_status()

        # 获取执行历史
        execution_history = agent_service.get_execution_history(limit=20)

        # 获取统计信息
        statistics = agent_service.get_agent_statistics()

        return render_template('agent/dashboard.html',
                             agent_status=agent_status,
                             execution_history=execution_history,
                             statistics=statistics)
    except Exception as e:
        return render_template('error.html', error=str(e))

@agent_bp.route('/api/status')
@admin_required
def api_status():
    """获取Agent状态API"""
    try:
        agent_service = get_agent_service()
        if not agent_service:
            return jsonify({'error': 'Agent服务未初始化'}), 500

        status = agent_service.get_agent_status()
        return jsonify(status)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@agent_bp.route('/api/run/<agent_name>', methods=['POST'])
@admin_required
def api_run_agent(agent_name):
    """手动执行Agent API"""
    try:
        agent_service = get_agent_service()
        if not agent_service:
            return jsonify({'error': 'Agent服务未初始化'}), 500

        result = agent_service.manual_run_agent(agent_name)
        return jsonify(result)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@agent_bp.route('/api/history/<agent_name>')
@admin_required
def api_agent_history(agent_name):
    """获取Agent执行历史API"""
    try:
        limit = request.args.get('limit', 20, type=int)
        agent_service = get_agent_service()
        if not agent_service:
            return jsonify({'error': 'Agent服务未初始化'}), 500

        history = agent_service.get_execution_history(agent_name, limit)
        return jsonify(history)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@agent_bp.route('/api/statistics')
@admin_required
def api_statistics():
    """获取统计信息API"""
    try:
        agent_service = get_agent_service()
        if not agent_service:
            return jsonify({'error': 'Agent服务未初始化'}), 500

        stats = agent_service.get_agent_statistics()
        return jsonify(stats)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@agent_bp.route('/notifications')
@login_required
def notifications():
    """通知页面"""
    try:
        user_id = session.get('user_id')
        notification_service = NotificationService()
        
        # 获取用户通知
        notifications = notification_service.get_user_notifications(user_id, limit=50)
        
        # 获取未读数量
        unread_count = notification_service.get_unread_count(user_id)
        
        return render_template('agent/notifications.html',
                             notifications=notifications,
                             unread_count=unread_count)
    except Exception as e:
        return render_template('error.html', error=str(e))

@agent_bp.route('/api/notifications/mark_read/<int:notification_id>', methods=['POST'])
@login_required
def api_mark_notification_read(notification_id):
    """标记通知为已读API"""
    try:
        user_id = session.get('user_id')
        notification_service = NotificationService()
        notification_service.mark_notification_read(notification_id, user_id)
        return jsonify({'success': True})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@agent_bp.route('/api/notifications/unread_count')
@login_required
def api_unread_count():
    """获取未读通知数量API"""
    try:
        user_id = session.get('user_id')
        notification_service = NotificationService()
        count = notification_service.get_unread_count(user_id)
        return jsonify({'count': count})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@agent_bp.route('/recommendations')
@login_required
def recommendations():
    """智能推荐页面"""
    try:
        user_id = session.get('user_id')
        agent_service = get_agent_service()

        if not agent_service:
            return render_template('agent/recommendations.html',
                                 recommendations=[],
                                 error='Agent服务未初始化')

        # 获取用户推荐
        recommendations = agent_service.get_user_recommendations(user_id)

        return render_template('agent/recommendations.html',
                             recommendations=recommendations)
    except Exception as e:
        return render_template('error.html', error=str(e))

@agent_bp.route('/analysis')
@admin_required
def analysis():
    """数据分析结果页面"""
    try:
        agent_service = get_agent_service()

        if not agent_service:
            return render_template('agent/analysis.html',
                                 analysis_data={},
                                 error='Agent服务未初始化')

        # 获取分析数据
        analysis_data = agent_service.get_analysis_data()

        return render_template('agent/analysis.html',
                             analysis_data=analysis_data)
    except Exception as e:
        return render_template('error.html', error=str(e))

@agent_bp.route('/api/recommendations')
@login_required
def api_recommendations():
    """获取用户推荐API"""
    try:
        user_id = session.get('user_id')
        agent_service = get_agent_service()
        if not agent_service:
            return jsonify({'error': 'Agent服务未初始化'}), 500

        recommendations = agent_service.get_user_recommendations(user_id)
        return jsonify(recommendations)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@agent_bp.route('/config')
@admin_required
def config():
    """Agent配置页面"""
    try:
        agent_service = get_agent_service()
        if not agent_service:
            return render_template('error.html', error='Agent服务未初始化')

        # 获取当前配置
        config = agent_service.get_agent_config()

        return render_template('agent/config.html', config=config)
    except Exception as e:
        return render_template('error.html', error=str(e))

@agent_bp.route('/api/config', methods=['GET', 'POST'])
@admin_required
def api_config():
    """Agent配置API"""
    try:
        agent_service = get_agent_service()
        if not agent_service:
            return jsonify({'error': 'Agent服务未初始化'}), 500

        if request.method == 'GET':
            config = agent_service.get_agent_config()
            return jsonify(config)
        else:
            # 更新配置
            config_data = request.get_json()
            result = agent_service.update_agent_config(config_data)
            return jsonify(result)

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@agent_bp.route('/api/analysis')
@admin_required
def api_analysis_data():
    """获取分析数据API"""
    try:
        agent_service = get_agent_service()
        if not agent_service:
            return jsonify({'error': 'Agent服务未初始化'}), 500

        analysis_data = agent_service.get_analysis_data()
        return jsonify(analysis_data)
    except Exception as e:
        return jsonify({'error': str(e)}), 500
