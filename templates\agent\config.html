{% extends "base.html" %}

{% block title %}Agent配置{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <h1>Agent配置管理</h1>
                <p class="text-muted">配置Agent的运行参数和调度设置</p>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- 库存预警Agent配置 -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-exclamation-triangle"></i> 库存预警Agent
                    </h5>
                </div>
                <div class="card-body">
                    <form id="inventory-config-form">
                        <div class="form-group">
                            <label for="inventory-threshold">低库存阈值</label>
                            <input type="number" class="form-control" id="inventory-threshold" 
                                   value="{{ config.InventoryAlert.low_stock_threshold if config.InventoryAlert else 10 }}" min="1">
                            <small class="form-text text-muted">当库存低于此数量时发送预警</small>
                        </div>
                        <div class="form-group">
                            <label for="inventory-schedule">调度时间</label>
                            <input type="time" class="form-control" id="inventory-schedule" 
                                   value="{{ config.InventoryAlert.schedule_time if config.InventoryAlert else '08:00' }}">
                        </div>
                        <div class="form-group">
                            <label for="inventory-interval">执行间隔</label>
                            <select class="form-control" id="inventory-interval">
                                <option value="daily" {{ 'selected' if config.InventoryAlert and config.InventoryAlert.interval == 'daily' else '' }}>每日</option>
                                <option value="hourly" {{ 'selected' if config.InventoryAlert and config.InventoryAlert.interval == 'hourly' else '' }}>每小时</option>
                            </select>
                        </div>
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" id="inventory-enabled" 
                                   {{ 'checked' if config.InventoryAlert and config.InventoryAlert.enabled else '' }}>
                            <label class="form-check-label" for="inventory-enabled">启用Agent</label>
                        </div>
                        <button type="submit" class="btn btn-primary btn-sm mt-3">保存配置</button>
                    </form>
                </div>
            </div>
        </div>

        <!-- 智能推荐Agent配置 -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-lightbulb"></i> 智能推荐Agent
                    </h5>
                </div>
                <div class="card-body">
                    <form id="recommendation-config-form">
                        <div class="form-group">
                            <label for="recommendation-max">最大推荐数量</label>
                            <input type="number" class="form-control" id="recommendation-max" 
                                   value="{{ config.SmartRecommendation.max_recommendations if config.SmartRecommendation else 5 }}" min="1" max="10">
                            <small class="form-text text-muted">每个用户的最大推荐物资数量</small>
                        </div>
                        <div class="form-group">
                            <label for="recommendation-threshold">相似度阈值</label>
                            <input type="number" class="form-control" id="recommendation-threshold" 
                                   value="{{ config.SmartRecommendation.similarity_threshold if config.SmartRecommendation else 0.1 }}" 
                                   min="0" max="1" step="0.1">
                            <small class="form-text text-muted">推荐相似度的最低阈值</small>
                        </div>
                        <div class="form-group">
                            <label for="recommendation-schedule">调度时间</label>
                            <input type="time" class="form-control" id="recommendation-schedule" 
                                   value="{{ config.SmartRecommendation.schedule_time if config.SmartRecommendation else '22:00' }}">
                        </div>
                        <div class="form-group">
                            <label for="recommendation-interval">执行间隔</label>
                            <select class="form-control" id="recommendation-interval">
                                <option value="daily" {{ 'selected' if config.SmartRecommendation and config.SmartRecommendation.interval == 'daily' else '' }}>每日</option>
                                <option value="weekly" {{ 'selected' if config.SmartRecommendation and config.SmartRecommendation.interval == 'weekly' else '' }}>每周</option>
                            </select>
                        </div>
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" id="recommendation-enabled" 
                                   {{ 'checked' if config.SmartRecommendation and config.SmartRecommendation.enabled else '' }}>
                            <label class="form-check-label" for="recommendation-enabled">启用Agent</label>
                        </div>
                        <button type="submit" class="btn btn-success btn-sm mt-3">保存配置</button>
                    </form>
                </div>
            </div>
        </div>

        <!-- 数据分析Agent配置 -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar"></i> 数据分析Agent
                    </h5>
                </div>
                <div class="card-body">
                    <form id="analysis-config-form">
                        <div class="form-group">
                            <label for="analysis-period">分析周期(月)</label>
                            <input type="number" class="form-control" id="analysis-period" 
                                   value="{{ config.DataAnalysis.analysis_period_months if config.DataAnalysis else 3 }}" min="1" max="12">
                            <small class="form-text text-muted">数据分析的时间范围</small>
                        </div>
                        <div class="form-group">
                            <label for="analysis-schedule">调度时间</label>
                            <input type="text" class="form-control" id="analysis-schedule" 
                                   value="{{ config.DataAnalysis.schedule_time if config.DataAnalysis else 'monday.09:00' }}"
                                   placeholder="monday.09:00">
                            <small class="form-text text-muted">格式: monday.09:00 (周一上午9点)</small>
                        </div>
                        <div class="form-group">
                            <label for="analysis-interval">执行间隔</label>
                            <select class="form-control" id="analysis-interval">
                                <option value="weekly" {{ 'selected' if config.DataAnalysis and config.DataAnalysis.interval == 'weekly' else '' }}>每周</option>
                                <option value="daily" {{ 'selected' if config.DataAnalysis and config.DataAnalysis.interval == 'daily' else '' }}>每日</option>
                            </select>
                        </div>
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" id="analysis-enabled" 
                                   {{ 'checked' if config.DataAnalysis and config.DataAnalysis.enabled else '' }}>
                            <label class="form-check-label" for="analysis-enabled">启用Agent</label>
                        </div>
                        <button type="submit" class="btn btn-info btn-sm mt-3">保存配置</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- 全局操作 -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-cogs"></i> 全局操作
                    </h5>
                </div>
                <div class="card-body">
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-primary" onclick="saveAllConfigs()">
                            <i class="fas fa-save"></i> 保存所有配置
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="resetAllConfigs()">
                            <i class="fas fa-undo"></i> 重置为默认
                        </button>
                        <button type="button" class="btn btn-outline-info" onclick="exportConfigs()">
                            <i class="fas fa-download"></i> 导出配置
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 保存单个Agent配置
function saveAgentConfig(agentName, configData) {
    const fullConfig = {};
    fullConfig[agentName] = configData;
    
    $.ajax({
        url: '/agent/api/config',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(fullConfig),
        success: function(data) {
            if (data.success) {
                alert(`${agentName} 配置保存成功！`);
            } else {
                alert(`保存失败: ${data.error}`);
            }
        },
        error: function() {
            alert('保存配置时发生错误');
        }
    });
}

// 库存预警Agent配置表单
$('#inventory-config-form').on('submit', function(e) {
    e.preventDefault();
    
    const config = {
        low_stock_threshold: parseInt($('#inventory-threshold').val()),
        schedule_time: $('#inventory-schedule').val(),
        interval: $('#inventory-interval').val(),
        enabled: $('#inventory-enabled').is(':checked')
    };
    
    saveAgentConfig('InventoryAlert', config);
});

// 智能推荐Agent配置表单
$('#recommendation-config-form').on('submit', function(e) {
    e.preventDefault();
    
    const config = {
        max_recommendations: parseInt($('#recommendation-max').val()),
        similarity_threshold: parseFloat($('#recommendation-threshold').val()),
        schedule_time: $('#recommendation-schedule').val(),
        interval: $('#recommendation-interval').val(),
        enabled: $('#recommendation-enabled').is(':checked')
    };
    
    saveAgentConfig('SmartRecommendation', config);
});

// 数据分析Agent配置表单
$('#analysis-config-form').on('submit', function(e) {
    e.preventDefault();
    
    const config = {
        analysis_period_months: parseInt($('#analysis-period').val()),
        schedule_time: $('#analysis-schedule').val(),
        interval: $('#analysis-interval').val(),
        enabled: $('#analysis-enabled').is(':checked')
    };
    
    saveAgentConfig('DataAnalysis', config);
});

// 保存所有配置
function saveAllConfigs() {
    const allConfigs = {
        InventoryAlert: {
            low_stock_threshold: parseInt($('#inventory-threshold').val()),
            schedule_time: $('#inventory-schedule').val(),
            interval: $('#inventory-interval').val(),
            enabled: $('#inventory-enabled').is(':checked')
        },
        SmartRecommendation: {
            max_recommendations: parseInt($('#recommendation-max').val()),
            similarity_threshold: parseFloat($('#recommendation-threshold').val()),
            schedule_time: $('#recommendation-schedule').val(),
            interval: $('#recommendation-interval').val(),
            enabled: $('#recommendation-enabled').is(':checked')
        },
        DataAnalysis: {
            analysis_period_months: parseInt($('#analysis-period').val()),
            schedule_time: $('#analysis-schedule').val(),
            interval: $('#analysis-interval').val(),
            enabled: $('#analysis-enabled').is(':checked')
        }
    };
    
    $.ajax({
        url: '/agent/api/config',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(allConfigs),
        success: function(data) {
            if (data.success) {
                alert('所有配置保存成功！');
            } else {
                alert(`保存失败: ${data.error}`);
            }
        },
        error: function() {
            alert('保存配置时发生错误');
        }
    });
}

// 重置配置
function resetAllConfigs() {
    if (confirm('确定要重置所有配置为默认值吗？')) {
        location.reload();
    }
}

// 导出配置
function exportConfigs() {
    $.get('/agent/api/config', function(data) {
        const blob = new Blob([JSON.stringify(data, null, 2)], {type: 'application/json'});
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'agent_config.json';
        a.click();
        URL.revokeObjectURL(url);
    });
}
</script>
{% endblock %}
